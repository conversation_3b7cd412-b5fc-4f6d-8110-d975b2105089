"""
Python-Programm zur Erstellung eines positiven RSS-Feeds mit Sentiment-Analyse und Duplikatsprüfung
"""

# Basis-Imports
import base64
import deep_translator
import hashlib
import deep_translator.exceptions
import feedparser
import logging
import newspaper
import numpy as np
import os
import pandas as pd
import threading
import time
import io
import sys
from datetime import datetime, timezone, timedelta, date
from feedgen.feed import FeedGenerator
from sklearn.metrics.pairwise import cosine_similarity
from textblob_de import TextBlobDE
import json
import yaml
import colorlog
from logging.handlers import TimedRotatingFileHandler
from PIL import Image
from bs4 import BeautifulSoup
import re
from contextlib import contextmanager
import platform

# KI/ML-Imports
from sentence_transformers import SentenceTransformer
from transformers import pipeline

# SQLAlchemy-Imports
from sqlalchemy import create_engine, and_, inspect, or_, func, Column, text, insert
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.pool import StaticPool, NullPool
import requests
from jsonschema import validate, ValidationError
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, TimeoutError

# Add the project root directory to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(project_root)
from backend.app.core.config import settings
from backend.app.models.models import Entry

from flask import Flask
import logging
from dashboard import register_stats_routes, register_query_routes, register_entry_routes
from etl.shared.progress_tracker import thread_progress, update_progress


def start_flask_app():
    """Run the Flask app in a separate thread."""
    # Print all registered routes for debugging
    print("\n\n==== FLASK SERVER STARTING ====\n")
    print("Registered Flask routes:")
    for rule in app.url_map.iter_rules():
        print(f"Route: {rule.endpoint} - {rule.rule} - {rule.methods}")

    # Check if the templates directory exists
    template_path = os.path.join(os.getcwd(), 'templates')
    print(f"Looking for templates at: {template_path}")
    if os.path.exists(template_path):
        print(f"Templates directory exists: {template_path}")
        # List files in the templates directory
        files = os.listdir(template_path)
        print(f"Files in templates directory: {files}")
    else:
        print(f"Templates directory does not exist: {template_path}")

    print("\n==== STARTING FLASK SERVER ====\n")
    app.run(host='0.0.0.0', port=5000, debug=True, use_reloader=False)


# -----------------------------------------------------------------------------------------------
# NewsAggregator-Klasse
# ---------------------------------------------------------------------------------------------------

def _get_entries_in_process(mandatory_fields, analysis_fields, filters, parquet_path):
    from backend.app.core.config import settings
    from backend.app.models.models import Entry
    from sqlalchemy import create_engine, or_, and_, text
    from sqlalchemy.orm import sessionmaker
    from datetime import datetime, timedelta, timezone
    import logging
    import pyarrow as pa
    import pyarrow.parquet as pq
    db_url = settings.DATABASE_URL
    lookback_days = settings.NEWS_LOOKBACK_DAYS
    engine = create_engine(db_url)
    Session = sessionmaker(bind=engine)
    session = Session()
    query = session.query(Entry)
    # Apply mandatory fields
    if mandatory_fields:
        query = query.filter(*(getattr(Entry, field) != None for field in mandatory_fields))
    # Apply analysis fields
    if analysis_fields:
        query = query.filter(or_(*(getattr(Entry, field) == None for field in analysis_fields)))
    # Apply filters
    has_published_filter = False
    for field, op, value in filters:
        col = getattr(Entry, field)
        if field == 'published':
            has_published_filter = True
        if op == '>=':
            query = query.filter(col >= value)
        elif op == '>':
            query = query.filter(col > value)
        elif op == '<=':
            query = query.filter(col <= value)
        elif op == '<':
            query = query.filter(col < value)
        elif op == '==':
            query = query.filter(col == value)
        elif op == '!=':
            query = query.filter(col != value)
        else:
            raise ValueError(f"Unsupported operator: {op}")
    if not has_published_filter:
        one_day_ago = datetime.now(timezone.utc) - timedelta(days=lookback_days)
        query = query.filter(Entry.published >= one_day_ago)
    query = query.order_by(Entry.published.desc())
    compiled_query = query.statement.compile(
        dialect=session.bind.dialect,
        compile_kwargs={"literal_binds": True}
    )
    logging.info(str(compiled_query))
    entries = query.all()
    session.close()
    # Convert entries to list of dicts for serialization
    entry_dicts = [e.__dict__ for e in entries]
    # Remove all SQLAlchemy state columns if present
    for d in entry_dicts:
        d.pop('_sa_instance_state', None)
    table = pa.Table.from_pylist(entry_dicts)
    pq.write_table(table, parquet_path)
    return True

def _save_entries_in_process(df):
    import pandas as pd
    from sqlalchemy import create_engine, text
    from backend.app.core.config import settings
    from backend.app.models.models import Entry
    from datetime import datetime, timezone
    db_url = settings.DATABASE_URL
    datetime_string = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
    temp_table = f"temp_entries_{datetime_string}"
    merge_columns = ', '.join(df.columns)
    merge_values = ', '.join(f't.{col}' for col in df.columns)
    merge_sql = f"""
        MERGE INTO entries e
        USING {temp_table} t
        ON (e.entry_id = t.entry_id)
        WHEN NOT MATCHED THEN
        INSERT ({merge_columns})
        VALUES ({merge_values})
    """
    dtype_dict = {column.name: column.type for column in Entry.__table__.columns if column.name in df.columns}
    engine = create_engine(db_url)
    # 1. Write DataFrame to temp table
    df.to_sql(
        temp_table,
        engine,
        if_exists='replace',
        index=False,
        dtype=dtype_dict,
        chunksize=1000
    )
    # 2. Merge into main table
    with engine.begin() as conn:
        result = conn.execute(text(merge_sql))
        rowcount = result.rowcount
        print(f"Inserted {rowcount} of {len(df)} entries (with temp table cleanup)")
    # 3. Drop temp table
    with engine.begin() as conn:
        conn.execute(text(f"DROP TABLE {temp_table} PURGE"))
    return True

class NewsAggregator:
    def __init__(self, clear_last_entries=6, recreate_table=False):
        logging.info("Initialisiere NewsAggregator")
        # Use pool_recycle to avoid stale Oracle connections
        self.engine = create_engine(
            settings.DATABASE_URL,
            poolclass=NullPool,
            pool_recycle=1800  # Recycle connections every 30 minutes
        )
        self.Session = scoped_session(sessionmaker(bind=self.engine, expire_on_commit=False))
        self.new_sentiment_scores = threading.Event()

        if recreate_table:
            self._recreate_table()
        elif clear_last_entries:
            self._clear_last_entries(clear_last_entries)

        self._cleanup_temp_tables()

    @contextmanager
    def session_scope(self):
        """Provide a transactional scope around a series of operations."""
        session = self.Session()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            # Remove the session from the scoped_session registry to ensure a fresh one next time
            self.Session.remove()
            logging.error(f"Session error, rolled back and removed session: {e}")
            raise
        finally:
            session.close()

    def _cleanup_temp_tables(self):
        """Lösche alle Tabellen, die mit 'temp_entries_' beginnen."""
        logging.info("Lösche temporäre Tabellen...")
        session = self.Session()
        try:
            inspector = inspect(self.engine)
            temp_tables = [table for table in inspector.get_table_names() if table.startswith('temp_entries_')]
            for table_name in temp_tables:
                logging.info(f"Lösche Tabelle: {table_name}")
                session.execute(text(f"DROP TABLE IF EXISTS {table_name}"))
            session.commit()
        except Exception as e:
            logging.error(f"Fehler beim Löschen temporärer Tabellen: {e}")
            session.rollback()
        finally:
            session.close()


    def _clear_last_entries(self, clear_last_entries):
        """Lösche die letzten 6 Einträge aus der Datenbank zu Debugging-Zwecken"""
        logging.info(f"Lösche die letzten {clear_last_entries} Einträge aus der Datenbank")
        session = self.Session()
        try:
            last_entries = session.query(Entry).order_by(Entry.published.desc()).limit(clear_last_entries).all()
            for entry in last_entries:
                session.delete(entry)
            session.commit()
        except Exception as e:
            try:
                logging.error(e)
                session.rollback()
            except Exception as e:
                logging.error(e)
        finally:
            try:
                session.close()
            except Exception as e:
                logging.error(e)
                pass

    def _generate_entry_id(self, entry, source_config):
        """Eindeutige ID aus den in entry_id_from definierten Feldern generieren"""
        fields = source_config['entry_id_from'].split(',')
        id_parts = [getattr(entry, field.strip()) for field in fields]
        return hashlib.sha256('-'.join(id_parts).encode()).hexdigest()

    def _load_rss_sources(self):
        """RSS-Quellen aus der Konfigurationsdatei laden"""
        rss_file_path = settings.NEWS_RSS_SOURCES_FILE
        if not os.path.isabs(rss_file_path):
            rss_file_path = os.path.join('etl', rss_file_path)
        with open(rss_file_path, 'r', encoding='utf-8') as file:
            return yaml.safe_load(file)['rss_sources']

    def _clean_html_content(self, html_content):
        """Convert HTML content to clean text with proper formatting"""
        if not html_content:
            return ""

        try:
            # Handle encoding issues if html_content is bytes
            if isinstance(html_content, bytes):
                # Try different encodings in order of preference
                encodings = ['utf-8', 'iso-8859-1', 'windows-1252', 'latin-1']
                for encoding in encodings:
                    try:
                        html_content = html_content.decode(encoding)
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    # If all encodings fail, use utf-8 with error handling
                    html_content = html_content.decode('utf-8', errors='replace')

            # Parse HTML
            soup = BeautifulSoup(html_content, 'html.parser')

            # Replace <br>, <p>, </p>, </div> tags with newlines
            for tag in soup.find_all(['br', 'p', 'div']):
                tag.replace_with('\n' + tag.get_text() + '\n')

            # Get text and clean up
            text = soup.get_text()

            # Remove excessive whitespace/newlines while preserving paragraph structure
            text = re.sub(r'\n\s*\n', '\n\n', text)  # Replace multiple newlines with double newline
            text = re.sub(r' +', ' ', text)  # Replace multiple spaces with single space
            text = text.strip()  # Remove leading/trailing whitespace

            return text
        except Exception as e:
            logging.error(f"Error cleaning HTML content: {e}")
            # Return the original content as fallback, or empty string if it fails
            return str(html_content) if html_content else ""

    def download_feeds(self):
        """Download Feeds-Thread: Parse RSS feeds and insert content into database"""
        while True:
            logging.info("Starte Download Feeds-Thread")
            update_progress("Running")
            try:
                # Collect RSS feeds
                rss_sources = self._load_rss_sources()
                for source in rss_sources:
                    logging.info(f"{source['url']}")
                    update_progress("Processing", rss_sources.index(source) + 1, len(rss_sources))
                    feed = feedparser.parse(source['url'])
                    if feed.bozo:
                        logging.warning(f"Fehler beim Abrufen des RSS-Feeds: {source['url']} (Status: {feed.bozo_exception})")
                        continue
                    entries = []
                    for entry in feed.entries:
                        # Only set description if it exists and has content
                        description = getattr(entry, 'description', None)
                        cleaned_description = self._clean_html_content(description) if description else None
                        # Ensure empty descriptions become NULL
                        if cleaned_description and cleaned_description.strip():
                            final_description = cleaned_description
                        else:
                            final_description = None
                        
                        entries.append({
                            'entry_id': self._generate_entry_id(entry, source),
                            'title': entry.title,
                            'link': entry.link,
                            'source': source['url'],
                            'description': final_description,
                            'published': datetime(*entry.published_parsed[:6], tzinfo=timezone.utc)
                                if "published_parsed" in entry else None,
                        })
                    # Convert to DataFrame, removing immediate duplicates
                    df = pd.DataFrame(entries).drop_duplicates(subset=['entry_id'])
                    # Save entries, letting database handle duplicates
                    self._save_entries(df)
                logging.info(f"DONE - Warte {settings.INTERVAL_FEEDS} Sekunden")
                update_progress(f"Waiting {settings.INTERVAL_FEEDS}s")
                time.sleep(settings.INTERVAL_FEEDS)
            except Exception as e:
                logging.error(e)

    def _save_entries(self, df):
        """Save entries to database, ignoring duplicates"""
        if df.empty:
            return
        import multiprocessing
        p = multiprocessing.Process(
            target=_save_entries_in_process,
            args=(df,)
        )
        p.start()
        p.join(timeout=600)
        if p.is_alive():
            p.terminate()
            p.join()
            logging.error(f"Timeout while saving entries and cleaning up temp table")
            return

    def _get_entries(self, mandatory_fields, analysis_fields, filters=[]):
        """Helper function to fetch entries with specific fields and filters

        Args:
            mandatory_fields: List of fields that must not be None (list of Entry attribute names as strings)
            analysis_fields: List of fields that should be None (for analysis, list of Entry attribute names as strings)
            filters: List of tuples (field_name, op, value), e.g. [('llm_positive', '>=', 0.9)]
                If no published filter is provided, defaults to last 24 hours

        Returns:
            List of dicts representing entries
        """
        import multiprocessing
        import pyarrow.parquet as pq
        import uuid
        # Determine temp file path
        temp_dir = 'c:/temp' if platform.system() == 'Windows' else '/tmp'
        os.makedirs(temp_dir, exist_ok=True)
        temp_file = os.path.join(temp_dir, f'_get_entries_{uuid.uuid4().hex}.parquet')
        p = multiprocessing.Process(
            target=_get_entries_in_process,
            args=(mandatory_fields, analysis_fields, filters, temp_file)
        )
        p.start()
        p.join(timeout=900)
        if p.is_alive():
            p.terminate()
            p.join()
            if os.path.exists(temp_file):
                remove_with_retry(temp_file)
            raise TimeoutError("Timeout while fetching entries from database (process killed)")
        if not os.path.exists(temp_file):
            raise RuntimeError("No result parquet file created by _get_entries_in_process")
        table = pq.read_table(temp_file)
        remove_with_retry(temp_file)
        entries = table.to_pylist()
        return entries

    def download_full_texts(self):
        """Volltexte der Artikel herunterladen und in der Datenbank speichern"""
        def fetch_article(link):
            try:
                article = newspaper.Article(link)
                article.download()
                article.parse()
                return article.text if article.text else "<n/a>"
            except UnicodeDecodeError as e:
                logging.error(f"Encoding error for {link}: {e}")
                # Try alternative approach with requests and manual encoding handling
                try:
                    import requests
                    from bs4 import BeautifulSoup

                    response = requests.get(link, timeout=30)
                    # Try to detect encoding from response
                    if response.encoding is None or response.encoding == 'ISO-8859-1':
                        # Try to detect encoding from content
                        encodings = ['utf-8', 'iso-8859-1', 'windows-1252', 'latin-1']
                        for encoding in encodings:
                            try:
                                response.encoding = encoding
                                text_content = response.text
                                # Test if the encoding works by trying to encode it back
                                text_content.encode('utf-8')
                                break
                            except (UnicodeDecodeError, UnicodeEncodeError):
                                continue
                        else:
                            # Fallback to replacing problematic characters
                            response.encoding = 'utf-8'
                            text_content = response.text
                    else:
                        text_content = response.text

                    # Extract text content using BeautifulSoup
                    soup = BeautifulSoup(text_content, 'html.parser')
                    # Remove script and style elements
                    for script in soup(["script", "style"]):
                        script.decompose()
                    text = soup.get_text()
                    # Clean up whitespace
                    lines = (line.strip() for line in text.splitlines())
                    chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                    text = ' '.join(chunk for chunk in chunks if chunk)

                    return text if text else "<n/a>"
                except Exception as fallback_e:
                    logging.error(f"Fallback method also failed for {link}: {fallback_e}")
                    return "<n/a>"
            except Exception as e:
                logging.error(f"Error fetching article {link}: {e}")
                return "<n/a>"

        while True:
            logging.info("START")
            update_progress("Running")
            try:
                entries = self._get_entries(
                    mandatory_fields=[],
                    analysis_fields=["full_text"]
                )
                logging.info(f"ITEMS: {len(entries)}")

                with self.session_scope() as session:
                    for idx, entry in enumerate(entries):
                        entry_obj = Entry(**entry)
                        entry = session.merge(entry_obj)
                        with ThreadPoolExecutor() as executor:
                            try:
                                logging.info(f"{entry.title} -- {entry.link} -- {entry.published}")
                                update_progress("Running", progress=idx + 1, size=len(entries))
                                future = executor.submit(fetch_article, entry.link)
                                entry.full_text = future.result(timeout=30)
                                session.commit()
                            except newspaper.ArticleException as e:
                                logging.error(f"{entry.title} -- {entry.link} -- {e} -- set ''")
                                entry.full_text = "<n/a>"
                                session.commit()
                            except Exception as e:
                                logging.error(f"{entry.title} -- {entry.link} -- {e}")
                                pass

                logging.info(f"DONE - Warte {settings.INTERVAL_FULL_TEXTS} Sekunden")
                update_progress(f"Waiting {settings.INTERVAL_FULL_TEXTS}s")
                time.sleep(settings.INTERVAL_FULL_TEXTS)

            except Exception as e:
                logging.error(e)
                
    def translate_full_texts(self):
        """Übersetze Volltexte der Artikel und speichere in der Datenbank"""
        while True:
            logging.info("START")
            update_progress("Running")
            try:
                entries = self._get_entries(
                    mandatory_fields=["full_text"],
                    analysis_fields=["english_text"]
                )
                logging.info(f"ITEMS: {len(entries)}")
                my_translator = deep_translator.GoogleTranslator(source='de', target='en')

                with self.session_scope() as session:
                    for idx, entry in enumerate(entries):
                        entry_obj = Entry(**entry)
                        entry = session.merge(entry_obj)
                        try:
                            logging.info(f"{entry.title} -- {entry.link} -- {entry.published}")
                            update_progress("Running", progress=idx + 1, size=len(entries))
                            entry.english_text = my_translator.translate(entry.full_text[:4999])
                            session.commit()
                        except deep_translator.exceptions.TranslationNotFound as e:
                            logging.error(f"{entry.title} -- {entry.link} -- {e} -- set ''")
                            entry.english_text = "<n/a>"
                            session.commit()
                        except Exception as e:
                            logging.error(f"{entry.title} -- {entry.link} -- {e}")
                            pass

                logging.info(f"DONE - Warte {settings.INTERVAL_TRANSLATION} Sekunden")
                update_progress(f"Waiting {settings.INTERVAL_TRANSLATION}s")
                time.sleep(settings.INTERVAL_TRANSLATION)

            except Exception as e:
                logging.error(e)

    def compute_embeddings(self):
        """Compute embeddings for entries and save in the database"""
        logging.info("Initialisiere Embedding-Modell")
        embedding_model = SentenceTransformer('BAAI/bge-m3')

        while True:
            logging.info("START - Compute Embeddings")
            update_progress("Running")
            try:
                entries = self._get_entries(
                    mandatory_fields=["full_text"],
                    analysis_fields=["embedding"]
                )
                logging.info(f"ITEMS: {len(entries)}")

                with self.session_scope() as session:
                    for idx, entry in enumerate(entries):
                        entry_obj = Entry(**entry)
                        entry = session.merge(entry_obj)
                        logging.info(f"{entry.title} -- {entry.link} -- {entry.published}")
                        update_progress("Running", progress=idx + 1, size=len(entries))
                        embedding = embedding_model.encode([entry.full_text], convert_to_tensor=True).cpu().numpy()[0]
                        entry.embedding = embedding.tobytes()
                        session.commit()

                logging.info(f"DONE - Warte {settings.INTERVAL_EMBEDDING} Sekunden")
                update_progress(f"Waiting {settings.INTERVAL_EMBEDDING}s")
                time.sleep(settings.INTERVAL_EMBEDDING)

            except Exception as e:
                logging.error(e)

    def sentiment_analysis(self):
        """Sentiment-Analyse durchführen und Ergebnisse in der Datenbank speichern"""

        # Initialisierung der NLTK-Ressourcen
        # import nltk
        # nltk.download('all')

        # Initialisierung der Modelle
        logging.info("Initialisiere Modelle")
        from germansentiment import SentimentModel
        from nltk.sentiment.vader import SentimentIntensityAnalyzer
        sentiment_model = SentimentModel()
        sia = SentimentIntensityAnalyzer()

        while True:
            logging.info("START")
            update_progress("Running")
            try:
                entries = self._get_entries(
                    mandatory_fields=["full_text", "english_text"],
                    analysis_fields=[
                        "vader_pos", "vader_neu", "vader_neg", "vader_compound",
                        "german_sentiment_positive", "german_sentiment_neutral",
                        "german_sentiment_negative", "tbd_polarity", "tbd_subjectivity"
                    ]
                )
                logging.info(f"ITEMS: {len(entries)}")

                with self.session_scope() as session:
                    for idx, entry in enumerate(entries):
                        entry_obj = Entry(**entry)
                        entry = session.merge(entry_obj)
                        logging.info(f"{entry.title} -- {entry.link} -- {entry.published}")
                        update_progress("Running", progress=idx + 1, size=len(entries))

                        # Sentiment-Analyse mit VADER
                        vader_scores = sia.polarity_scores(entry.english_text)
                        entry.vader_pos = vader_scores['pos']
                        entry.vader_neu = vader_scores['neu']
                        entry.vader_neg = vader_scores['neg']
                        entry.vader_compound = vader_scores['compound']

                        # Sentiment-Analyse mit GermanSentiment
                        predictions, probabilities = sentiment_model.predict_sentiment([entry.full_text], output_probabilities=True)
                        probDict = dict(probabilities[0])
                        entry.german_sentiment_positive = probDict['positive']
                        entry.german_sentiment_neutral = probDict['neutral']
                        entry.german_sentiment_negative = probDict['negative']

                        # Sentiment-Analyse mit TextBlobDE
                        tbd = TextBlobDE(entry.full_text)
                        entry.tbd_polarity = tbd.sentiment.polarity
                        entry.tbd_subjectivity = tbd.sentiment.subjectivity

                        session.commit()

                    # Set the event after completing one iteration but only if at least one new entry
                    if entries:
                        self.new_sentiment_scores.set()

                logging.info(f"DONE - Warte {settings.INTERVAL_SENTIMENT} Sekunden")
                update_progress(f"Waiting {settings.INTERVAL_SENTIMENT}s")
                time.sleep(settings.INTERVAL_SENTIMENT)

            except Exception as e:
                logging.error(e)

    def llm_sentiment_ad(self):
        """Perform both sentiment analysis and ad check with LLM in one call
        LM Studio: qwen2.5-7b-instruct-1m@q4_k_m: 5500 context window"""

        LLM_PROMPT = (
            "Analyze the following text in two aspects:\n"
            "1. Is this an advertisement, promotional press release, or similar content?\n"
            "2. Is the message positive, uplifting, or encouraging? Does it report about joyful, appreciative coexistence?\n"
            "Provide sentiment probabilities that sum to 1.0 for positive, negative, and neutral.\n"
            "As reason explain in German.\n"
        )
        LLM_CALLS = 5
        LLM_SCHEMA = {
            "title": "CombinedAnalysisResult",
            "type": "object",
            "properties": {
                "advertisement": {
                    "type": "object",
                    "properties": {
                        "is_advertisement": {
                            "type": "number",
                            "minimum": 0,
                            "maximum": 1,
                            "description": "Likelihood score for text to be advertisement."
                        },
                        "reason": {
                            "type": "string",
                            "description": "Reason for advertisement estimate."
                        }
                    },
                    "required": ["is_advertisement", "reason"]
                },
                "sentiment": {
                    "type": "object",
                    "properties": {
                        "positive": {
                            "type": "number",
                            "minimum": 0,
                            "maximum": 1
                        },
                        "neutral": {
                            "type": "number",
                            "minimum": 0,
                            "maximum": 1
                        },
                        "negative": {
                            "type": "number",
                            "minimum": 0,
                            "maximum": 1
                        },
                        "reason": {
                            "type": "string"
                        }
                    },
                    "required": ["positive", "neutral", "negative", "reason"]
                }
            },
            "required": ["advertisement", "sentiment"]
        }

        while True:
            logging.info("START - Combined LLM Analysis")
            update_progress("Running")
            try:
                entries = self._get_entries(
                    mandatory_fields=["full_text"],
                    analysis_fields=[
                        "llm_is_ad", "llm_is_ad_reason",
                        "llm_positive", "llm_neutral", "llm_negative",
                        "llm_positive_std", "llm_neutral_std", "llm_negative_std",
                        "llm_positive_diff", "llm_neutral_diff", "llm_negative_diff",
                        "llm_reason_list", "llm_iterations", "llm_break_reason"
                    ]
                )
                logging.info(f"ITEMS: {len(entries)}")

                with self.session_scope() as session:
                    for idx, entry in enumerate(entries):
                        entry_obj = Entry(**entry)
                        entry = session.merge(entry_obj)
                        try:
                            logging.info(f"{entry.title} -- {entry.link} -- {entry.published}")
                            update_progress("Running", progress=idx + 1, size=len(entries))

                            scores_list = []
                            reason_list = []
                            breakReason = ""

                            # since n-parameter not working for Qwen and/or LM Studio
                            for llm_iteration in range(LLM_CALLS):
                                input_text = f"{entry.title}\n{entry.description}\n{entry.full_text}"[:settings.NEWS_LLM_MAX_INPUT_LENGTH]
                                data, provider, model = self._call_llm(
                                    prompt=LLM_PROMPT,
                                    schema=LLM_SCHEMA,
                                    input_text=input_text,
                                    max_tokens=800
                                )

                                # Store advertisement result on first iteration
                                if llm_iteration == 0:
                                    entry.llm_is_ad = data['advertisement']['is_advertisement']
                                    entry.llm_is_ad_reason = data['advertisement']['reason']

                                # Collect sentiment scores
                                scores = {k: v for k,v in data['sentiment'].items() if k in ["positive", "neutral", "negative"]}
                                scores_list.append(scores)
                                reason_list.append(data['sentiment']["reason"])

                                # Calculate running statistics
                                if len(scores_list) > 0:
                                    # Calculate average and std deviation
                                    avg_scores = {key: np.mean([score[key] for score in scores_list]) for key in scores_list[0]}
                                    std_scores = {key: np.std([score[key] for score in scores_list]) for key in scores_list[0]}
                                    # Compute difference between one shot result (index 0) and average result
                                    diff_scores = {key: abs(scores_list[0][key] - avg_scores[key]) for key in scores_list[0]}

                                    # Early stopping criteria
                                    oneArgsort = np.argsort(list(scores_list[0].values()))
                                    avgArgsort = np.argsort(list(avg_scores.values()))
                                    avgScores = np.array(list(avg_scores.values()))
                                    stdScores = np.array(list(std_scores.values()))
                                    # Stop if score is clearly not positive
                                    if avg_scores['positive'] <= 0.5:
                                        breakReason = f"clearly not positive ({llm_iteration})"
                                        break
                                    # Stop if difference between one shot result and average result is small
                                    # and does not change outcome
                                    if len(scores_list) > 1 and \
                                            max([score for score in diff_scores.values()]) < 0.05 and \
                                            oneArgsort[-1] == avgArgsort[-1]:
                                        breakReason = f"non-diverging result ({len(scores_list)})"
                                        break
                                    # Stop if standard deviation too small to change sentiment, i.e. if
                                    # the largest sentiment average value minus its standard deviation is larger than
                                    # the next largest sentiment average value plus its standard deviation
                                    if len(scores_list) > 2 and \
                                            (avgScores[avgArgsort[-1]] - stdScores[avgArgsort[-1]] >
                                            avgScores[avgArgsort[-2]] + stdScores[avgArgsort[-2]]):
                                        breakReason = f"converged result ({len(scores_list)})"
                                        break
                                    if entry.llm_is_ad >= 0.5:
                                        breakReason = f"advertisement"
                                        break

                            else:
                                breakReason = f"max iterations ({LLM_CALLS})"

                            # Save sentiment analysis results
                            entry.llm_positive = avg_scores['positive']
                            entry.llm_neutral = avg_scores['neutral']
                            entry.llm_negative = avg_scores['negative']
                            entry.llm_positive_std = std_scores['positive']
                            entry.llm_neutral_std = std_scores['neutral']
                            entry.llm_negative_std = std_scores['negative']
                            entry.llm_positive_diff = diff_scores['positive']
                            entry.llm_neutral_diff = diff_scores['neutral']
                            entry.llm_negative_diff = diff_scores['negative']
                            entry.llm_reason_list = "; ".join(reason_list)
                            entry.llm_iterations = len(scores_list)
                            entry.llm_break_reason = breakReason
                            entry.llm_model = model
                            entry.llm_provider = provider

                            session.commit()
                        except Exception as e:
                            logging.error(f"{e} -- {entry.title}")
                            continue

                logging.info(f"DONE - Warte {settings.INTERVAL_LLM} Sekunden")
                update_progress(f"Waiting {settings.INTERVAL_LLM}s")
                time.sleep(settings.INTERVAL_LLM)

            except Exception as e:
                logging.error(e)

    def classify_iptc_newscode(self):
        """Classify entries into IPTC NewsCodes using Hugging Face model"""
        classifier = pipeline("text-classification", model="classla/multilingual-IPTC-news-topic-classifier")

        while True:
            logging.info("START")
            update_progress("Running")
            try:
                entries = self._get_entries(
                    mandatory_fields=["full_text"],
                    analysis_fields=["iptc_newscode", "iptc_score"]
                )
                logging.info(f"ITEMS: {len(entries)}")

                with self.session_scope() as session:
                    for idx, entry in enumerate(entries):
                        entry_obj = Entry(**entry)
                        entry = session.merge(entry_obj)
                        logging.info(f"{entry.title} -- {entry.link} -- {entry.published}")
                        update_progress("Running", progress=idx + 1, size=len(entries))
                        try:
                            maxLength = classifier.model.config.max_position_embeddings
                            input_text = f"{entry.title}\n{entry.description}\n{entry.full_text}"[:maxLength]
                            result = classifier(input_text)
                            entry.iptc_newscode = result[0]['label']
                            entry.iptc_score = result[0]['score']
                            session.commit()
                        except Exception as e:
                            logging.error(e)
                            continue

                logging.info(f"DONE - Warte {settings.INTERVAL_IPTC_CLASSIFICATION} Sekunden")
                update_progress(f"Waiting {settings.INTERVAL_IPTC_CLASSIFICATION}s")
                time.sleep(settings.INTERVAL_IPTC_CLASSIFICATION)

            except Exception as e:
                logging.error(e)

    def check_and_update_duplicates(self):
        """Check for duplicates and update the dup_entry_id"""
        while True:
            logging.info("START - Duplicate Check")
            update_progress("Running")
            try:
                entries = self._get_entries(
                    mandatory_fields=["embedding"],
                    analysis_fields=["dup_entry_id", "dup_entry_conf"]
                )
                logging.info(f"ITEMS: {len(entries)}")

                if not entries:
                    logging.info("No new entries to check for duplicates")
                    time.sleep(settings.INTERVAL_DUPLICATE_CHECK)
                    continue

                existing_entries = self._get_entries(
                    mandatory_fields=["embedding", "dup_entry_id"],
                    analysis_fields=[],
                    filters=[
                        ("published", ">=", datetime.now(timezone.utc) - timedelta(days=settings.NEWS_DUPLICATE_LOOKBACK_DAYS))
                    ]
                )
                logging.info(f"EXISTING ITEMS: {len(existing_entries)}")
                if not existing_entries:
                    logging.info("No existing entries to compare for duplicates")
                    time.sleep(settings.INTERVAL_DUPLICATE_CHECK)
                    continue

                existing_embeddings = np.array([np.frombuffer(ex_entry['embedding'], dtype=np.float32) for ex_entry in existing_entries])
                existing_dup_ids = [ex_entry["dup_entry_id"] for ex_entry in existing_entries]

                with self.session_scope() as session:
                    for idx, entry in enumerate(entries):
                        entry_obj = Entry(**entry)
                        entry = session.merge(entry_obj)
                        logging.info(f"{entry.title} -- {entry.link} -- {entry.published}")
                        update_progress("Running", progress=idx + 1, size=len(entries))
                        new_embedding = np.frombuffer(entry.embedding, dtype=np.float32)
                        similarities = cosine_similarity([new_embedding], existing_embeddings)[0]
                        most_similar_index = np.argmax(similarities)
                        similarity = similarities[most_similar_index]
                        if similarity > settings.NEWS_SIMILARITY_THRESHOLD:
                            entry.dup_entry_id = existing_dup_ids[most_similar_index]
                            entry.dup_entry_conf = similarity
                        else:
                            entry.dup_entry_id = entry.entry_id
                        existing_embeddings = np.vstack([existing_embeddings, new_embedding])
                        existing_dup_ids.append(entry.dup_entry_id)
                        session.commit()

                logging.info(f"DONE - Warte {settings.INTERVAL_DUPLICATE_CHECK} Sekunden")
                update_progress(f"Waiting {settings.INTERVAL_DUPLICATE_CHECK}s")
                time.sleep(settings.INTERVAL_DUPLICATE_CHECK)
            except Exception as e:
                logging.error(e)

    def generate_descriptions(self):
        """Generate German descriptions for entries that have full text but no description using LLM"""
        LLM_PROMPT = (
            "Du bist ein Assistent, der prägnante Artikelbeschreibungen erstellt.\n"
            "Erstelle eine kurze, sachliche Beschreibung (max. 2-3 Sätze) auf Deutsch, "
            "die die Hauptpunkte des Artikels zusammenfasst.\n"
            "Konzentriere dich auf die wichtigsten Informationen und behalte einen positiven und optimistischen Ton bei."
        )
        LLM_SCHEMA = {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string",
                    "description": "Eine prägnante Beschreibung des Artikels auf Deutsch"
                }
            },
            "required": ["description"]
        }

        while True:
            logging.info("START - Generate German Descriptions")
            update_progress("Running")
            try:
                entries = self._get_entries(
                    mandatory_fields=["full_text"],
                    analysis_fields=["description"],
                    filters=[
                        ("llm_positive", ">=", settings.NEWS_SENTIMENT_THRESHOLD),
                    ]
                )
                logging.info(f"ITEMS: {len(entries)}")

                with self.session_scope() as session:
                    for idx, entry in enumerate(entries):
                        entry_obj = Entry(**entry)
                        entry = session.merge(entry_obj)
                        logging.info(f"{entry.title} -- {entry.link} -- {entry.published}")
                        update_progress("Running", progress=idx + 1, size=len(entries))
                        try:
                            input_text = f"Titel: {entry.title}\nArtikel: {entry.full_text}"
                            data, provider, model = self._call_llm(
                                prompt=LLM_PROMPT,
                                schema=LLM_SCHEMA,
                                input_text=input_text,
                                max_tokens=200,
                                temperature=0.3
                            )
                            entry.description = data['description']
                            entry.description_auto_generated = True
                            session.commit()
                            logging.info(f"Generated description for {entry.title}: {entry.description[:100]}...")
                        except (requests.RequestException, json.JSONDecodeError, ValidationError) as e:
                            logging.error(f"Error generating description for {entry.title}: {e}")
                            continue

                logging.info(f"DONE - Warte {settings.INTERVAL_DESCRIPTION_GENERATION} Sekunden")
                update_progress(f"Waiting {settings.INTERVAL_DESCRIPTION_GENERATION}s")
                time.sleep(settings.INTERVAL_DESCRIPTION_GENERATION)

            except Exception as e:
                logging.error(e)

    def generate_image_prompts(self):
        """Generate creative prompts for positive, uplifting lead images for news articles"""
        LLM_URL = "http://127.0.0.1:1234/v1/chat/completions"
        LLM_PROMPT = (
            "What would be an adequate picture to accompany the given news article? "
            "Choose one and describe it purely as a list of at most 10 keywords. "
            "The keywords must be English. "
            "Avoid specific names, use generic/functional descriptions. "
            "The keywords must be separated by comma. "
        )
        LLM_SCHEMA = {
            "type": "object",
            "properties": {
                "keywords": {
                "type": "string",
                "description": "comma-separated English keywords"
                }
            },
            "required": [
                "keywords"
            ]
        }

        while True:
            logging.info("START - Generate Image Prompts")
            update_progress("Running")
            try:
                entries = self._get_entries(
                    mandatory_fields=["full_text"],
                    analysis_fields=["image_prompt"],
                    filters=[
                        ("llm_positive", ">=", settings.NEWS_SENTIMENT_THRESHOLD),
                        ("llm_is_ad", "==", 0)
                    ]
                )
                logging.info(f"ITEMS: {len(entries)}")

                with self.session_scope() as session:
                    for idx, entry in enumerate(entries):
                        entry_obj = Entry(**entry)
                        entry = session.merge(entry_obj)
                        logging.info(f"{entry.title} -- {entry.link} -- {entry.published}")
                        update_progress("Running", progress=idx + 1, size=len(entries))
                        try:
                            input_text = f"Title: {entry.title}\nArticle: {entry.full_text}"[:settings.NEWS_LLM_MAX_INPUT_LENGTH]
                            data, provider, model = self._call_llm(
                                prompt=LLM_PROMPT,
                                schema=LLM_SCHEMA,
                                input_text=input_text,
                                max_tokens=200,
                                temperature=0.7
                            )
                            if ',' not in data['keywords']:
                                pass
                            entry.image_prompt = data['keywords']
                            session.commit()
                            logging.info(f"Generated image prompt for {entry.title}: {entry.image_prompt[:100]}...")
                        except (requests.RequestException, json.JSONDecodeError, ValidationError) as e:
                            logging.error(f"Error generating image prompt for {entry.title}: {e}")
                            continue

                logging.info(f"DONE - Warte {settings.INTERVAL_IMAGE_PROMPT_GENERATION} Sekunden")
                update_progress(f"Waiting {settings.INTERVAL_IMAGE_PROMPT_GENERATION}s")
                time.sleep(settings.INTERVAL_IMAGE_PROMPT_GENERATION)

            except Exception as e:
                logging.error(e)

    def generate_preview_images(self):
        """Generate preview images for positive news items and store them in the database using Stable Diffusion"""
        from openai import OpenAI

        while True:
            logging.info("START - Generate Preview Images")
            update_progress("Running")
            try:
                entries = self._get_entries(
                    mandatory_fields=["full_text", "image_prompt"],
                    analysis_fields=["preview_img"],
                    filters=[
                        ("llm_positive", ">=", settings.NEWS_SENTIMENT_THRESHOLD),
                        ("llm_is_ad", "==", 0)
                    ]
                )
                logging.info(f"ITEMS: {len(entries)}")

                with self.session_scope() as session:
                    for idx, entry in enumerate(entries):
                        entry_obj = Entry(**entry)
                        entry = session.merge(entry_obj)
                        logging.info(f"Generating image for: {entry.title} -- {entry.link}")
                        update_progress("Running", progress=idx + 1, size=len(entries))

                        try:
                            # google not available in Europe
                            providers = ['stable_diffusion', 'ionos']
                            
                            for provider in providers:
                                try:
                                    image_data, model = self._generate_image_provider(
                                        provider, entry.image_prompt)
                                except Exception as e:
                                    logging.warning(f"Image generation with {provider} failed: {e}")
                                    continue
                                else:
                                    break
                            else:
                                raise Exception("All image generation providers failed")
                            
                            # Save the image to the database
                            entry.preview_img = image_data
                            entry.preview_model = model
                            entry.preview_provider = provider
                            session.commit()
                            logging.info(f"{provider} generated and saved image for: {entry.title}")

                        except Exception as e:
                            logging.error(f"Error generating image for {entry.title}: {e}")
                            continue

                # Wait before checking for new entries
                logging.info(f"DONE - Warte {settings.INTERVAL_PREVIEW_IMAGE} Sekunden")
                update_progress(f"Waiting {settings.INTERVAL_PREVIEW_IMAGE}s")
                time.sleep(settings.INTERVAL_PREVIEW_IMAGE)

            except Exception as e:
                logging.error(f"{e}")
            finally:
                try:
                    session.close()
                except Exception as e:
                    logging.error(e)
                    pass

    def _generate_image_provider(self, provider, prompt):
        """Generate image using specified provider
        
        Args:
            provider: The image provider ('stable_diffusion', 'flux')
            prompt: The image generation prompt
            
        Returns:
            Tuple of (image_data, generator_name)
        """
        if provider == 'stable_diffusion':
            STABLE_DIFFUSION_URL = "http://127.0.0.1:7860"
            negative_prompt = "face, logo, brands, text, watermark, signature, blur, out of focus, deformed, bad anatomy, disfigured, poorly drawn face, mutated, extra limb, ugly, poorly drawn hands, missing limb, floating limbs, disconnected limbs, malformed hands, long neck, long body, mutated hands and fingers"
            payload = {
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "steps": 20,
                "width": 688,
                "height": 400,
                "cfg_scale": 7,
                "sampler_name": "DPM++ 2M Karras"
            }
            response = requests.post(
                url=f"{STABLE_DIFFUSION_URL}/sdapi/v1/txt2img",
                json=payload
            )
            response.raise_for_status()
            r = response.json()
            image_data = base64.b64decode(r['images'][0])
            return image_data, "stable_diffusion_local"
            
        elif provider == 'ionos':
            from openai import OpenAI
            openai = OpenAI(
                api_key=settings.IONOS_API_KEY,
                base_url='https://openai.inference.de-txl.ionos.com/v1'
            )
            response = openai.images.generate(
                model='black-forest-labs/FLUX.1-schnell',
                prompt=f"an illustration with {prompt}",
                size='1024*1024'
            )
            image_data = base64.b64decode(response.data[0].b64_json)
            return image_data, "flux1_schnell_ionos"
        elif provider == 'google':
            try:
                model = "gemini-2.0-flash-preview-image-generation"
                api_key = settings.GOOGLE_API_KEY
                url = f"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent?key={api_key}"
                headers = {
                    "Content-Type": "application/json"
                }
                data = {
                    "contents": [{
                        "parts": [
                            {"text": f"an illustration with {prompt}"}
                        ]
                    }],
                    "generationConfig": {"responseModalities": ["TEXT","IMAGE"]}
                }

                response = requests.post(url, headers=headers, json=data)
                response.raise_for_status()

                response_json = response.json()
                image_data = base64.b64decode(response_json["candidates"][0]["content"]["parts"][1]['inlineData']['data'])
                return image_data, model

            except Exception as e:
                logging.error(f"Error generating image with Google Gemini: {e}")
                raise
        else:
            raise ValueError(f"Unsupported provider: {provider}")

    def generate_rss_feed(self):
        """RSS-Feed generieren und speichern"""
        while True:
            logging.info("START")
            update_progress("Running")
            try:
                today = datetime.now(timezone.utc).date()
                entries = self._get_entries(
                    mandatory_fields=[],
                    analysis_fields=[],
                    filters=[
                        ("llm_positive", ">=", settings.NEWS_SENTIMENT_THRESHOLD),
                        ("published", ">=", datetime.now(timezone.utc).date())
                    ]
                )
                unique_entries = self._check_duplicates(entries)
                self._generate_rss_feed(unique_entries)
                self.new_sentiment_scores.clear()

                # Wait for some time before rearming and again waiting for the event to be set
                logging.info(f"DONE - {len(unique_entries)} Einträge - Warte {settings.INTERVAL_RSS_FEED} Sekunden")
                time.sleep(settings.INTERVAL_RSS_FEED)
                logging.info("WAIT - Bis neue Sentiment-Scores verfügbar sind")
                update_progress(f"Waiting for new sentiment scores")
                self.new_sentiment_scores.wait()
            except Exception as e:
                logging.error(e)

    def _check_duplicates(self, df):
        """Semantische Duplikatsprüfung mit Embeddings"""
        logging.info("Prüfe auf semantische Duplikate")
        embeddings = np.array([np.frombuffer(embedding, dtype=np.float32) for embedding in df["embedding"]])

        duplicate_filter = np.ones(len(df), dtype=bool)
        for i, (_, row) in enumerate(df.iterrows()):
            if not duplicate_filter[i]:
                continue
            similarities = cosine_similarity([embeddings[i]], embeddings)[0]
            # find all duplicates beyond match with itself
            duplicates = np.where(similarities > settings.NEWS_SIMILARITY_THRESHOLD)[0]  # Changed from SIMILARITY_THRESHOLD
            if len(duplicates) > 1:
                logging.info(f"Semantische Duplikate gefunden mit Similarity {similarities[duplicates]}")
                duplicates = sorted(duplicates, key=lambda idx: df.iloc[idx].published, reverse=True)
                # Keep the most recent one
                for idx in duplicates[1:]:
                    duplicate_filter[idx] = False
                    row2 = df.iloc[idx]
                    logging.debug(f"Entferne Duplikat: {row2['title']} (published: {row2['published']})")

        logging.info(f"Semantische Duplikate entfernt: {len(df)-sum(duplicate_filter)} von {len(df)}")
        return df[duplicate_filter]

    def _generate_rss_feed(self, unique_entries):
        """Ausgabefeed generieren"""
        logging.info("Generiere Ausgabefeed")
        fg = FeedGenerator()
        fg.title('Positiver Nachrichten-Feed')
        fg.description('Aggregierter Feed mit positiven Nachrichten')
        fg.link(href='http://example.com/positive-feed.rss', rel='self')

        for _, row in unique_entries.iterrows():
            fe = fg.add_entry()
            fe.title(row['title'])
            fe.link(href=row['link'])
            fe.description(row['description'])
            if row['published']:
                fe.pubDate(row['published'])

        fg.rss_file('positive_feed.rss')

    def _call_llm_provider(self, provider: str, prompt: str, schema: dict, input_text: str, max_tokens: int = 400, temperature: float = 0.1, top_p = 0.95) -> dict:
        """Centralized function for making LLM API calls to various providers

        Args:
            provider: The LLM provider ('local', 'ionos', 'groq', 'openrouter')
            prompt: System prompt for the LLM
            schema: JSON schema for response validation
            input_text: User input text
            max_tokens: Maximum tokens in response (default: 400)
            temperature: Temperature for response generation (default: 0.1)

        Returns:
            Validated JSON response from LLM

        Raises:
            requests.RequestException: If API call fails
            json.JSONDecodeError: If response is not valid JSON
            ValidationError: If response doesn't match schema
        """
        input_text = input_text[:settings.NEWS_LLM_MAX_INPUT_LENGTH]  # Truncate input if needed

        if provider == 'local':
            model = "qwen2.5-7b-instruct-1m@q4_k_m"
            url = "http://127.0.0.1:1234/v1/chat/completions"
            headers = {}
            payload = {
                "messages": [
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": input_text}
                ],
                "model": model,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "top_p": top_p,
                "frequency_penalty": 0,
                "presence_penalty": 0,
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {"name": "LlmResponse", "strict": True, "schema": schema}
                },
            }
        elif provider == 'ionos':
            # apparently completely ignores response_format and/or tools
            model = "meta-llama/Meta-Llama-3.1-8B-Instruct"
            model='meta-llama/Llama-3.3-70B-Instruct'
            model='mistralai/Mixtral-8x7B-Instruct-v0.1'
            model='openGPT-X/Teuken-7B-instruct-commercial'
            model='meta-llama/CodeLlama-13b-Instruct-hf'
            model='meta-llama/Meta-Llama-3.1-405B-Instruct-FP8'
            model='mistralai/Mistral-7B-Instruct-v0.3'
            url = 'https://openai.inference.de-txl.ionos.com/v1/chat/completions'
            headers = {"Authorization": f"Bearer {settings.IONOS_API_KEY}"}
            # Define your tool using the schema
            tool = {
                "type": "function",
                "function": {
                    "name": "return_combined_analysis_result",
                    "description": "Returns an analysis of advertisement likelihood and sentiment scores.",
                    "parameters": schema
                }
            }
            payload = {
                "messages": [
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": input_text}
                ],
                "model": model,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "top_p": top_p,
                "frequency_penalty": 0,
                "presence_penalty": 0,
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {"strict": True, "schema": schema},
                "tools": [tool],
                "tool_choice": {
                    "type": "function",
                    "function": {"name": "return_combined_analysis_result"}
                },
                },
            }

        elif provider == 'groq':
            # only supports json_object which does not support strict validation
            model = "meta-llama/llama-4-scout-17b-16e-instruct"
            url = "https://api.groq.com/openai/v1/chat/completions"
            headers = {"Authorization": f"Bearer {settings.GROQ_API_KEY}"}
            payload = {
                "messages": [
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": input_text}
                ],
                "model": model,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "top_p": top_p,
                "frequency_penalty": 0,
                "presence_penalty": 0,
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {"name": "LlmResponse", "strict": True, "schema": schema}
                },
            }
        elif provider == 'openrouter':
            url = "https://openrouter.ai/api/v1/chat/completions"
            headers = {"Authorization": f"Bearer {settings.OPENROUTER_API_KEY}",
                       "Content-Type": "application/json"}
            # model = "qwen/qwen-2.5-7b-instruct:free" # as opposed to usage in lm studio, does not respect schema
            # model = "qwen/qwen-2.5-vl-7b-instruct:free" # same as normal 7b
            # model = "qwen/qwen3-8b:free" # reasoning, not working/too long
            # model = "meta-llama/llama-4-maverick:free" # does only support schema without min/max (xgrammar)
            # model = "meta-llama/llama-4-scout:free" # untested but likely same as maverick
            # model = "nvidia/llama-3.3-nemotron-super-49b-v1:free" # does not respect schema
            # model = "nvidia/llama-3.1-nemotron-ultra-253b-v1:free" # does not respect schema
            # model = "google/gemma-3-27b-it:free" # does not support json_schema at all (despite openrouter claiming it does)
            model = "google/learnlm-1.5-pro-experimental:free"  # works well
            payload = {
                "messages": [
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": input_text}
                ],
                "model": model,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "top_p": top_p,
                "frequency_penalty": 0,
                "presence_penalty": 0,
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {"name": "LlmResponse", "strict": True, "schema": schema}
                },
            }
        elif provider == 'chutes':
            model = "Qwen/Qwen3-32B"  # works well
            url = "https://llm.chutes.ai/v1/chat/completions"
            headers = {"Authorization": f"Bearer {settings.CHUTES_API_KEY}"}
            payload = {
                "messages": [
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": input_text}
                ],
                "model": model,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "top_p": top_p,
                "frequency_penalty": 0,
                "presence_penalty": 0,
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {"name": "LlmResponse", "strict": True, "schema": schema}
                },
            }
        else:
            raise ValueError(f"Unsupported provider: {provider}")

        # Make API call
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        print(f"{provider}: {input_text.split("\n")[0]}\n -> {response.json()['choices'][0]['message']['content']}")
        response.raise_for_status()

        # Parse and validate response
        response_dict = response.json()
        message_content = response_dict['choices'][0]['message']['content']
        data = json.loads(message_content)
        validate(instance=data, schema=schema)

        return data, model

    def _call_llm(self, prompt: str, schema: dict, input_text: str, max_tokens: int = 400, temperature: float = 0.1, top_p = 0.95) -> dict:
        """Centralized function for making LLM API calls with fallback logic"""
        providers = ['local', 'chutes', 'openrouter']
        for provider in providers:
            try:
                response, model = self._call_llm_provider(provider, prompt, schema, input_text, max_tokens, temperature, top_p)
                return response, provider, model  # Return response and provider
            except Exception as e:
                logging.warning(f"{provider} LLM failed: {e}")
        raise RuntimeError("All LLM providers failed")

def remove_with_retry(path, retries=10, delay=0.1):
    """Attempt to remove a file with retries to handle Windows file lock issues."""
    for _ in range(retries):
        try:
            os.remove(path)
            return
        except PermissionError:
            time.sleep(delay)
        except FileNotFoundError:
            return
    raise PermissionError(f"Could not remove file after {retries} attempts: {path}")

if __name__ == "__main__":

    # Logging-Konfiguration
    class CustomColoredFormatter(colorlog.ColoredFormatter):
        def format(self, record):
            colorMap = {'DownloadFeedsThread': 'white,bg_light_black',
                        'DownloadFullTextsThread': 'white,bg_green',
                        'ComputeEmbeddingsThread': 'white,bg_green',
                        'TranslateFullTextsThread': 'white,bg_yellow',
                        'SentimentAnalysisThread': 'white,bg_light_green',
                        'LLMSentimentAdThread': 'white,bg_light_purple',
                        'GenerateRssFeedThread': 'white,bg_light_white',
                        'ClassifyIPTCNewsCodeThread': 'white,bg_light_yellow',
                        'DuplicateCheckThread': 'white,bg_light_cyan',
                        'GeneratePreviewImagesThread': 'white,bg_light_blue',
                        'GenerateImagePromptsThread': 'white,bg_blue'}
            levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
            self.secondary_log_colors = {'threadName': {}}
            for k, v in colorMap.items():
                if record.threadName == k:
                    self.secondary_log_colors = \
                        {'threadName': {level: v for level in levels}}
            return super().format(record)

    formatter = CustomColoredFormatter(
        "%(log_color)s%(asctime)s - %(levelname)s - [%(threadName_log_color)s%(threadName)s%(reset)s%(log_color)s] - %(message)s",
        log_colors={
            'DEBUG': 'white',
            'INFO': 'cyan',
            'WARNING': 'yellow',
            'ERROR': 'red',
            'CRITICAL': 'red,bg_white',
        }
    )

    console_handler = colorlog.StreamHandler(open(1, 'w', encoding='utf-8-sig', closefd=False))
    # console_handler.setStream(open(1, 'w', encoding='utf-8', closefd=False))
    console_handler.setFormatter(formatter)

    file_handler = TimedRotatingFileHandler('c:/temp/news_aggregator.log', when='midnight', interval=1, encoding="utf-8")
    file_handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - [%(threadName)s] - %(message)s"))
    file_handler.suffix = "%Y-%m-%d"

    # Setup logging, flask dashboard logging should be limited to errors
    logging.basicConfig(level=logging.INFO, handlers=[file_handler, console_handler])
    # logging.getLogger('werkzeug').setLevel(logging.ERROR)

    # Load configuration from YAML file
    # with open('shared/news_aggregator.yaml', 'r') as file:
    #     config = yaml.safe_load(file)

    # Initialize Dashboard
    app = Flask(__name__, template_folder=os.path.join(os.path.dirname(__file__), 'templates'))
    register_stats_routes(app, settings)
    register_query_routes(app, settings)
    register_entry_routes(app, settings)

    # Initialisierung des NewsAggregators
    logging.info("Starte NewsAggregator")
    aggregator = NewsAggregator(clear_last_entries=0)

    # Start Flask app in a separate thread
    threading.Thread(target=start_flask_app, name="FlaskAppThread", daemon=True).start()

    # Threads für die Hauptfunktionen
    threading.Thread(target=aggregator.download_feeds, name="DownloadFeedsThread").start()
    threading.Thread(target=aggregator.download_full_texts, name="DownloadFullTextsThread").start()
    threading.Thread(target=aggregator.translate_full_texts, name="TranslateFullTextsThread").start()
    threading.Thread(target=aggregator.sentiment_analysis, name="SentimentAnalysisThread").start()
    threading.Thread(target=aggregator.llm_sentiment_ad, name="LLMSentimentAdThread").start()
    threading.Thread(target=aggregator.classify_iptc_newscode, name="ClassifyIPTCNewsCodeThread").start()
    threading.Thread(target=aggregator.check_and_update_duplicates, name="DuplicateCheckThread").start()
    threading.Thread(target=aggregator.compute_embeddings, name="ComputeEmbeddingsThread").start()
    # threading.Thread(target=aggregator.generate_rss_feed, name="GenerateRssFeedThread").start()
    threading.Thread(target=aggregator.generate_preview_images, name="GeneratePreviewImagesThread").start()
    threading.Thread(target=aggregator.generate_descriptions, name="GenerateDescriptionsThread").start()
    threading.Thread(target=aggregator.generate_image_prompts, name="GenerateImagePromptsThread").start()

    # Keep main thread alive
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logging.info("Received KeyboardInterrupt")
