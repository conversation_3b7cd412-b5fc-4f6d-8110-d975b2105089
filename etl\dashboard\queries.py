import os
import json
import logging
import requests
from flask import jsonify, request, render_template
from jsonschema import validate, ValidationError
from datetime import datetime, date
from sqlalchemy import text
from etl.dashboard.stats import create_session

def register_query_routes(app, config):
    # Add this at the top of the function to define the new queries directory
    queries_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'queries')

    @app.route('/sql_query', methods=['GET'])
    def sql_query_page():
        """Serve the SQL query page."""
        try:
            logging.info("Serving sql_query_fullwidth.html")
            return render_template('sql_query_fullwidth.html')
        except Exception as e:
            logging.error(f"Error serving SQL query page: {e}")
            return f"Error serving SQL query page: {str(e)}", 500

    @app.route('/execute_query', methods=['POST'])
    def execute_query():
        """Execute a SQL query against the Oracle database."""
        try:
            data = request.json
            query = data.get('query')
            
            if not query:
                return jsonify({"error": "No query provided"})
            
            # Create a session without passing config
            with create_session() as connection:
                result = connection.execute(text(query))
                columns = [str(col) for col in result.keys()]
                
                rows = []
                for row in result.fetchall():
                    processed_row = []
                    for item in row:
                        if isinstance(item, (datetime, date)):
                            processed_row.append(item.isoformat())
                        elif isinstance(item, (bytes, bytearray)):
                            processed_row.append("<binary data>")
                        elif hasattr(item, 'read'):
                            try:
                                data = item.read()
                                processed_row.append(data.decode('utf-8', errors='replace') if isinstance(data, bytes) else str(data))
                            except Exception as lob_error:
                                processed_row.append(f"<LOB data: {str(lob_error)}>")
                        else:
                            try:
                                json.dumps(item)
                                processed_row.append(item)
                            except (TypeError, OverflowError):
                                processed_row.append(str(item))
                    rows.append(processed_row)

                return jsonify({
                    "columns": columns,
                    "rows": rows
                })
        except Exception as e:
            logging.error(f"Error executing query: {e}")
            return jsonify({"error": str(e)})

    @app.route('/save_query', methods=['POST'])
    def save_query():
        """Save a SQL query to a file."""
        try:
            data = request.json
            name = data.get('name')
            query = data.get('query')

            if not name or not query:
                return jsonify({"error": "Name and query are required"})

            # Replace the queries directory definition
            if not os.path.exists(queries_dir):
                os.makedirs(queries_dir)

            file_path = os.path.join(queries_dir, f"{name}.sql")
            with open(file_path, 'w') as f:
                f.write(query)

            return jsonify({"success": True})
        except Exception as e:
            logging.error(f"Error saving query: {e}")
            return jsonify({"error": str(e)})

    @app.route('/load_query/<name>', methods=['GET'])
    def load_query(name):
        """Load a SQL query from a file."""
        try:
            if '/' in name or '\\' in name or '..' in name:
                return jsonify({"error": "Invalid query name"})

            # Replace the file path construction
            file_path = os.path.join(queries_dir, f"{name}.sql")
            if not os.path.exists(file_path):
                return jsonify({"error": "Query not found"})

            with open(file_path, 'r') as f:
                query = f.read()

            return jsonify({"query": query})
        except Exception as e:
            logging.error(f"Error loading query: {e}")
            return jsonify({"error": str(e)})

    @app.route('/list_queries', methods=['GET'])
    def list_queries():
        """List all saved queries."""
        try:
            # Replace the queries directory definition
            if not os.path.exists(queries_dir):
                os.makedirs(queries_dir)
                return jsonify({"queries": []})

            query_files = [f[:-4] for f in os.listdir(queries_dir) if f.endswith('.sql')]
            return jsonify({"queries": query_files})
        except Exception as e:
            logging.error(f"Error listing queries: {e}")
            return jsonify({"error": str(e)})

    @app.route('/generate_query', methods=['POST'])
    def generate_query():
        """Generate a SQL query using the LLM based on a natural language description."""
        try:
            data = request.json
            description = data.get('description')

            if not description:
                return jsonify({"error": "No description provided"})

            LLM_URL = "http://127.0.0.1:1234/v1/chat/completions"
            LLM_PROMPT = (
                "You are an expert SQL query generator for an Oracle database. "
                "Generate a SQL query based on the user's description. "
                "The database contains news articles with sentiment analysis in the ENTRIES table. "
                "Here's the structure of the main table:\n"
                "ENTRIES: entry_id, title, link, source, description, published, full_text, english_text, "
                "embedding, vader_pos, vader_neu, vader_neg, vader_compound, "
                "german_sentiment_positive, german_sentiment_neutral, german_sentiment_negative, "
                "tbd_polarity, tbd_subjectivity, llm_positive, llm_neutral, llm_negative, "
                "llm_positive_std, llm_neutral_std, llm_negative_std, llm_positive_diff, llm_neutral_diff, "
                "llm_negative_diff, llm_reason_list, llm_iterations, llm_break_reason, llm_is_ad, "
                "llm_is_ad_reason, iptc_newscode, iptc_score, dup_entry_id, dup_entry_conf\n\n"
                "SOURCES: source, name\n\n"
                "CATEGORIES: iptc_newscode, category\n\n"
                "Provide only the SQL query without any explanation or additional text."
            )

            LLM_SCHEMA = {
                "$schema": "https://json-schema.org/draft/2020-12/schema",
                "title": "SQLQueryResponse",
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "The SQL query that fulfills the user's request."
                    }
                },
                "required": ["query"]
            }

            try:
                response = requests.post(LLM_URL, json={
                    "messages": [
                        {"role": "system", "content": LLM_PROMPT},
                        {"role": "user", "content": description}
                    ],
                    "model": "qwen2.5-7b-instruct-1m@q4_k_m",
                    "max_tokens": 500,
                    "temperature": 0.1,
                    "top_p": 0.95,
                    "frequency_penalty": 0,
                    "presence_penalty": 0,
                    "response_format": {
                        "type": "json_schema",
                        "json_schema": {"strict": "true", "schema": LLM_SCHEMA}
                    },
                })
                response.raise_for_status()
                response_dict = response.json()
                message_content = response_dict['choices'][0]['message']['content']

                try:
                    data = json.loads(message_content)
                    validate(instance=data, schema=LLM_SCHEMA)
                    return jsonify({"query": data["query"]})
                except json.JSONDecodeError as e:
                    logging.error(f"Invalid response content: {e}")
                    if "SELECT" in message_content:
                        return jsonify({"query": message_content.strip()})
                    return jsonify({"error": "Failed to parse LLM response"})
                except ValidationError as e:
                    logging.error(f"Invalid response schema: {e}")
                    return jsonify({"error": "LLM response did not match expected format"})

            except requests.RequestException as e:
                logging.error(f"Error while calling LLM: {e}")
                return jsonify({"error": "Failed to connect to LLM service"})

        except Exception as e:
            logging.error(f"Error generating query: {e}")
            return jsonify({"error": str(e)})




